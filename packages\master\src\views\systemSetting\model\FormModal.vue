<template>
  <a-modal
    v-model:open="dialog.visible"
    :title="!!detail ? '编辑模型' : '添加模型及版本'"
    width="50%"
    @cancel="dialog.cancel"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="模型名称" name="modelName" required>
        <a-input
          v-model:value="formState.modelName"
          :disabled="isUpdate ? false : !!detail"
          placeholder="请输入模型名称"
        />
      </a-form-item>
      <a-form-item label="专业软件" name="desProfessionalSoftwareId" required>
        <a-select
          v-model:value="formState.desProfessionalSoftwareId"
          :disabled="!!detail"
          :options="
            modelStore.software.map((item: any) => ({
              label: item.softwareName,
              value: item.desProfessionalSoftwareId,
            }))
          "
          @change="(_, option: any) => (formState.softwareCode = option.label)"
          placeholder="请选择"
        />
      </a-form-item>
      <a-form-item label="模型文件" name="modelPath" required>
        <a-upload
          :file-list="modelFileList"
          name="file"
          :max-count="1"
          :disabled="isUpdate"
          :multiple="false"
          :before-upload="handleBeforeUpload"
        >
          <a-button :disabled="isUpdate">
            <upload-outlined></upload-outlined>
            点击上传
          </a-button>
        </a-upload>
      </a-form-item>
      <a-form-item label="模型图片" name="modelImage">
        <a-upload
          :file-list="modelImageList"
          name="file"
          accept="image/*"
          :disabled="isUpdate"
          :max-count="1"
          :multiple="false"
          :before-upload="handleImageBeforeUpload"
        >
          <a-button :disabled="isUpdate">
            <upload-outlined></upload-outlined>
            点击上传
          </a-button>
        </a-upload>
      </a-form-item>
      <a-form-item label="模型描述" name="remark">
        <a-textarea
          v-model:value="formState.remark"
          :rows="4"
          placeholder="请输入模型描述"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <div>
        <a-button @click="dialog.cancel">取消</a-button>
        <a-button type="primary" :loading @click="handldSubmit">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { addModel, updateModel } from '@/master/apis/model-tool';
import { ModelRecord } from '@/master/types/model';
import { message } from 'ant-design-vue';
import { useDialog } from 'dialog-async';
import useModelStore from '@/master/stores/model';
import UploadOutlined from '~icons/ant-design/upload-outlined';
import { uploadModel, uploadModelImage } from '@/master/apis/model-management';

const { detail, isUpdate } = defineProps<{
  detail?: ModelRecord;
  isUpdate?: boolean;
}>();

const dialog = useDialog();
const modelFileList = ref<any[]>([]);
const modelImageList = ref<any[]>([]);
const modelStore = useModelStore();
const loading = ref(false);
const formRef = ref();

const formState = ref<Partial<ModelRecord>>({
  ...detail,
  parentId: isUpdate ? undefined : detail?.sysDesModelId,
});

const handleBeforeUpload = (file: any) => {
  modelFileList.value = [file];
  formState.value.modelPath = file.name;
  if (!formState.value.modelName) {
    formState.value.modelName = file.name;
  }
  return false;
};

const handleImageBeforeUpload = (file: any) => {
  modelImageList.value = [file];
  formState.value.modelImage = file.name;
  return false;
};

const handldSubmit = () => {
  formRef.value
    .validate()
    .then(async () => {
      loading.value = true;
      if (isUpdate) {
        await updateModel(formState.value);
        message.success('新建成功!');
        loading.value = false;
        dialog.submit();
      } else {
        const { sysDesModelId } = await addModel(formState.value);
        formState.value.sysDesModelId = sysDesModelId;
        await uploadModel({
          file: modelFileList.value[0],
          sysDesModelId: formState.value.sysDesModelId!,
          desProfessionalSoftwareId: formState.value.desProfessionalSoftwareId!,
        });
        await uploadModelImage(
          modelImageList.value[0],
          formState.value.sysDesModelId!
        );
        message.success('添加成功!');
        loading.value = false;
        dialog.submit();
      }
    })
    .catch((error: any) => {
      message.error('error', error);
      loading.value = false;
    });
};
</script>
