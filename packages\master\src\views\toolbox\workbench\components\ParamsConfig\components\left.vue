<template>
  <div class="w-[400px] h-full">
    <a-card title="参数配置">
      <div class="overflow-y-auto" style="height: calc(100vh - 495px)">
        <div class="com-tree">
          <ParamList
            title="输入参数"
            :params="toolParams.inputParams"
            @select="handleSelect"
            @remove="name => handleRemove('input', name)"
            @remove-all="handleRemoveAll('input')"
          >
            <template #icon>
              <MaterialSymbolsInput class="text-blue-400" />
            </template>
          </ParamList>
          <ParamList
            title="输出参数"
            :params="toolParams.outputParams"
            @select="handleSelect"
            @remove="name => handleRemove('output', name)"
            @remove-all="handleRemoveAll('output')"
          >
            <template #icon>
              <MaterialSymbolsOutput class="text-orange-400" />
            </template>
          </ParamList>
        </div>
      </div>
      <a-tabs v-model:active-key="activeKey">
        <a-tab-pane key="1" tab="属性"></a-tab-pane>
        <template #rightExtra>
          <a-button type="link" @click="btnAddParams"> 添加参数 </a-button>
        </template>
      </a-tabs>
      <FormContent ref="formRef" @data-change="handleParamChange" />
    </a-card>
  </div>
</template>

<script setup lang="ts">
import MaterialSymbolsOutput from '~icons/material-symbols/output';
import MaterialSymbolsInput from '~icons/material-symbols/input';
import { showDialog } from 'dialog-async';
import AddModel from './addModel.vue';
import FormContent from './form-content.vue';
import ParamList from './ParamList.vue';
import { useToolContext } from '../../../useTool';
import type { Params } from '@/master/types/tool';
const activeKey = ref('1');

const { toolParams } = useToolContext();
const formRef = useTemplateRef('formRef');

const handleSelect = (e: Params) => {
  formRef.value?.setState(e);
};

const handleRemove = (type: 'input' | 'output', name: string) => {
  const paramsKey = type === 'input' ? 'inputParams' : 'outputParams';
  toolParams.value[paramsKey] = toolParams.value[paramsKey].filter(
    item => item.name !== name
  );
};

const handleRemoveAll = (type: 'input' | 'output') => {
  const paramsKey = type === 'input' ? 'inputParams' : 'outputParams';
  toolParams.value[paramsKey] = [];
};

const handleParamChange = (data: Params) => {
  const paramsKey = data.type === 'in' ? 'inputParams' : 'outputParams';
  const index = toolParams.value[paramsKey].findIndex(
    item => item.name === data.name
  );
  if (index !== -1) {
    toolParams.value[paramsKey][index] = data;
  }
};

// 添加参数
const btnAddParams = () => {
  showDialog(h(AddModel))
    .then((res: any) => {
      toolParams.value.inputParams.push(res);
    })
    .catch(() => {});
};
</script>
