import { ParamType, MethodType } from '../views/workbench/constants';

interface ToolBase {
  commands: string;
  componentIcon: string;
  componentName: string;
  componentOrder: number;
  componentStatus: ToolStatus;
  componentType: ToolType;
  desProfessionalSoftwareId: string;
  enable: 0 | 1;
  inputParams: string;
  outputParams: string;
  jobId?: number;
  nodeType: number;
  pageStructure: string;
  paramsMap: string;
  professionalSoftwareType?: string;
  remarks: string;
  sysDesComponentId: string;
  sysDesComponentTypeId: string;
  sysDesModelId: string;
  timeoutDuration: number;
}

export interface Tool extends ToolBase, Global.Entity {}

export interface ToolUpdateDto extends Tool {
  status: string;
}
export interface ToolCreateParams extends ToolBase {
  inputParams: Params[];
  outputParams: Params[];
  paramsMap: ParamsMap[];
  commands: Commands[];
  pageStructure: any[];
  bsflag: 'Y' | 'N';
  componentIcon?: {
    type: 'text' | 'image';
    content: string;
    backgroundColor?: string;
    color?: string;
  };
}
export interface ToolCreateDto extends ToolBase {
  enable?: 0 | 1;
  componentIcon?: string;
}

export interface ToolCategory extends Global.Entity {
  name: string;
  icon: string;
  description: string;
  sysDesComponentTypeId: string;
  status?: string;
  children?: ToolCategory[];
}

export enum ToolStatus {
  WAIT_APPROVAL = '0',
  PUBLISHED = '1',
}

export interface Params {
  name: string;
  value?: string;
  unit: string;
  type: string;
  label: string;
  title: string;
  key: string;
  defaultValue?: string;
}
export interface ParamsMap {
  paramType: ParamType;
  paramCode: string;
  command: string;
  dataFromType: string;
  dataFromCommand: string;
}
export interface Command {
  method: MethodType;
  name: string;
  unit: string;
  sortNo: number;
  isReturnToParam: boolean;
  value: string;
}
