<template>
  <a-card :title="name" class="tool-category-card">
    <!-- 空状态 -->
    <a-empty
      v-if="!children?.length"
      :image="simpleImage"
      description="暂无组件"
      class="empty-state"
    />

    <!-- 工具网格 -->
    <div v-else class="tools-grid">
      <div
        v-for="(item, index) in children"
        :key="`${item.sysDesComponentId}-${index}`"
        class="tool-item"
        @click="handleToolClick(item)"
      >
        <!-- 工具图标 -->
        <div class="tool-icon">
          <Icon
            v-if="isObjectString(item.componentIcon)"
            :value="parsedIcon(item.componentIcon)"
          />
          <component
            v-else
            :is="getToolIcon(item.componentIcon)"
            class="icon-component"
          />
        </div>

        <!-- 工具名称 -->
        <p class="tool-name" :title="item.componentName">
          {{ item.componentName }}
        </p>

        <!-- 状态标签 -->
        <div class="tool-status">
          <a-tag
            :color="item.componentStatus === '1' ? 'success' : 'error'"
            size="small"
          >
            {{ item.componentStatus === '1' ? '已发布' : '未发布' }}
          </a-tag>
        </div>

        <!-- 操作遮罩 -->
        <div class="tool-mask">
          <div class="mask-content">
            <!-- 主要操作按钮 -->
            <div class="primary-action">
              <a-button
                :type="item.componentStatus === '1' ? 'default' : 'primary'"
                size="small"
                class="status-btn"
                @click.stop="handleUpdateStatus(item)"
              >
                {{ item.componentStatus === '1' ? '取消发布' : '发布' }}
              </a-button>
            </div>

            <!-- 次要操作按钮 -->
            <div class="secondary-actions">
              <a-tooltip title="编辑" placement="top">
                <a-button
                  v-if="item.componentType === TOOL_TYPE.SOFT"
                  type="text"
                  size="small"
                  class="action-btn edit-btn"
                  @click.stop="handleEdit(item)"
                >
                  <AntDesignEditOutlined />
                </a-button>
              </a-tooltip>

              <a-tooltip title="删除" placement="top">
                <a-popconfirm
                  title="确定删除该组件吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  placement="top"
                  @confirm="handleDelete(item)"
                  @click.stop
                >
                  <a-button
                    type="text"
                    size="small"
                    class="action-btn delete-btn"
                    danger
                  >
                    <DeleteOutlined />
                  </a-button>
                </a-popconfirm>
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import AntDesignEditOutlined from '~icons/ant-design/edit-outlined';
import DeleteOutlined from '~icons/ant-design/delete-outlined';
import { Empty, message } from 'ant-design-vue';
import { updateTool, updateToolStatus } from '@/master/apis/tools';
import { getIcon } from '@/master/views/toolbox/icons';
import type { Tool } from '@/master/types/tool';
import Icon from 'shared/components/Icon/index.vue';
import { isObjectString } from 'shared/utils';
import { ToolType } from '@/master/views/toolbox/workbench/constants';

// 常量
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const TOOL_TYPE = ToolType;

// 路由
const router = useRouter();

// 组件属性
const { name, children } = defineProps<{
  name: string;
  status?: string;
  children?: Tool[];
}>();

// 事件定义
const emits = defineEmits<{
  reload: [];
}>();

// 工具函数
const parsedIcon = (icon: string) => {
  try {
    return JSON.parse(icon);
  } catch {
    return null;
  }
};

const getToolIcon = (iconName: string) => {
  if (!iconName) return null;
  return getIcon(iconName) || null;
};

// 事件处理函数
const handleToolClick = (item: Tool) => {
  // 可以在这里添加工具点击的默认行为
  console.log('Tool clicked:', item.componentName);
};

const handleEdit = (item: Tool) => {
  router.push({
    path: '/workbench',
    query: {
      sysDesComponentId: item.sysDesComponentId,
      professionalSoftwareId: item.desProfessionalSoftwareId,
    },
  });
};

const handleUpdateStatus = async (item: Tool) => {
  try {
    await updateToolStatus({
      sysDesComponentId: item.sysDesComponentId,
      status: item.componentStatus === '1' ? '0' : '1',
    });
    message.success('操作成功');
    emits('reload');
  } catch (error) {
    message.error('操作失败');
  }
};

const handleDelete = async (item: Tool) => {
  try {
    await updateTool({
      sysDesComponentId: item.sysDesComponentId,
      bsflag: 'Y',
    });
    message.success('删除成功');
    emits('reload');
  } catch (error) {
    message.error('删除失败');
  }
};
</script>

<style lang="less" scoped>
.tool-category-card {
  height: 100%;

  .empty-state {
    padding: 40px 0;
  }

  .tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
    max-height: 300px;
    overflow-y: auto;
    padding: 4px;
  }

  .tool-item {
    position: relative;
    width: 100px;
    height: 120px; // 增加高度以容纳状态标签
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    padding: 8px;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      .tool-mask {
        opacity: 1;
      }
    }

    .tool-icon {
      width: 40px; // 稍微减小图标尺寸
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 6px;

      .icon-component {
        font-size: 36px; // 减小图标字体大小
        color: var(--primary-color);
      }
    }

    .tool-name {
      font-size: 11px;
      text-align: center;
      margin: 0 0 4px 0;
      line-height: 1.2;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: rgba(0, 0, 0, 0.85);
    }

    .tool-status {
      margin-bottom: 4px;

      .ant-tag {
        margin: 0;
        font-size: 10px;
        padding: 1px 4px;
        line-height: 1.2;
      }
    }

    .tool-mask {
      position: absolute;
      inset: 0;
      background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.7),
        rgba(0, 0, 0, 0.5)
      );
      border-radius: 8px;
      opacity: 0;
      transition: all 0.3s ease;
      backdrop-filter: blur(4px);

      .mask-content {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 8px;
        padding: 8px;
      }

      .primary-action {
        width: 100%;
        display: flex;
        justify-content: center;

        .status-btn {
          border: none;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
          font-size: 11px;
          font-weight: 500;
          height: 28px;
          padding: 0 12px;
          border-radius: 14px;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
          }

          &[type='primary'] {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            border: 1px solid rgba(255, 255, 255, 0.2);

            &:hover {
              background: linear-gradient(135deg, #40a9ff, #69c0ff);
            }
          }

          &[type='default'] {
            background: rgba(255, 255, 255, 0.9);
            color: #666;
            border: 1px solid rgba(255, 255, 255, 0.3);

            &:hover {
              background: rgba(255, 255, 255, 1);
              color: #333;
            }
          }
        }
      }

      .secondary-actions {
        display: flex;
        gap: 4px;
        justify-content: center;

        .action-btn {
          width: 28px;
          height: 28px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.8);
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);

          &:hover {
            transform: translateY(-1px) scale(1.05);
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }

          &.edit-btn:hover {
            background: rgba(24, 144, 255, 0.2);
            border-color: rgba(24, 144, 255, 0.4);
            color: #40a9ff;
          }

          &.delete-btn:hover {
            background: rgba(255, 77, 79, 0.2);
            border-color: rgba(255, 77, 79, 0.4);
            color: #ff7875;
          }

          .anticon {
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
