import { ref } from 'vue';
import type { DebugLog, DebugBinding } from './types';

export function useDebugLog() {
  const debugLogs = ref<DebugLog[]>([]);

  function addLog(message: string): void {
    debugLogs.value.push({
      time: new Date().toLocaleTimeString(),
      log: message,
    });
  }

  function logDebugStart(bindings: DebugBinding[]): void {
    addLog('开始调试...');

    const inputLogs = bindings.map(
      binding => `${binding.props.label}:${binding.props.value}`
    );
    addLog(`输入参数: ${inputLogs.join('；')}`);
    // 日志已添加
  }

  function logSoftwareRunning(): void {
    addLog('软件运行中，请稍候...');
  }

  function logDebugComplete(): void {
    addLog('调试结束，输出结果已返回');
  }

  function clearLogs(): void {
    debugLogs.value = [];
  }

  return {
    debugLogs,
    addLog,
    logDebugStart,
    logSoftwareRunning,
    logDebugComplete,
    clearLogs,
  };
}
