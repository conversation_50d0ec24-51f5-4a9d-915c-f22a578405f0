<template>
  <div class="h-full">
    <a-card title="模型管理">
      <template #extra>
        <a-space>
          <a-button
            type="primary"
            :disabled="btnDisabled"
            @click="handleGetTreeData"
          >
            解析模型
          </a-button>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <GridiconsAdd />
            </template>
            添加模型
          </a-button>
        </a-space>
      </template>
      <a-table
        :data-source="data?.records"
        :loading
        :columns="tableColumns"
        :scroll="{ y: tableHeight }"
        :row-key="record => record.sysDesModelId"
        :row-selection="rowSelection"
        :pagination="false"
        size="small"
      >
      </a-table>
      <div class="flex justify-end mt-2">
        <a-pagination
          v-model:current="pagination.current"
          v-model:pageSize="pagination.size"
          show-size-changer
          :total="pagination.total"
          @change="onShowSizeChange"
        />
      </div>
    </a-card>
  </div>
</template>

<script setup lang="tsx">
import { message, TableColumnsType, TableProps } from 'ant-design-vue';
import { queryByPage } from '@/master/apis/model-tool';
import { showDialog } from 'dialog-async';
import FormModal from './FormModal.vue';
import { deleteModel, requestModelTree } from '@/master/apis/model-tool';
import dayjs from 'dayjs';
import { ModelRecord } from '@/master/types/model';
import { useRequest } from 'vue-hooks-plus';

const btnDisabled = computed(() => !selectedRow.value);

const pagination = ref({
  current: 1,
  size: 20,
  total: 0,
});

const tableHeight = computed(() => document.documentElement.clientHeight - 260);

const tableColumns: TableColumnsType<ModelRecord> = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center',
    customRender: ({ index }: { index: number }) => index + 1,
    fixed: 'left',
  },
  {
    title: '模型名称',
    dataIndex: 'modelName',
    align: 'center',
    minWidth: 160,
    fixed: 'left',
    customRender: ({ record }) => `${record.modelName}-${record.modelVersion}`,
  },
  {
    title: '专业软件',
    dataIndex: 'softwareCode',
    align: 'center',
    minWidth: 120,
    customRender: ({ value }) => <a-tag>{value}</a-tag>,
  },
  { title: '模型路径', dataIndex: 'modelPath', align: 'center', minWidth: 200 },
  {
    title: '模型截图',
    dataIndex: 'modelImage',
    align: 'center',
    minWidth: 120,
    customRender: ({ value }) =>
      value && <a-image src={value} height={32} alt="模型截图" />,
  },
  { title: '创建人', dataIndex: 'createUser', minWidth: 120, align: 'center' },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'center',
    minWidth: 140,
    customRender: opt =>
      opt.value ? dayjs(opt.value).format('YYYY-MM-DD HH:mm:ss') : '--',
  },
  { title: '描述', dataIndex: 'remark', minWidth: 160 },
  {
    title: '操作',
    key: 'action',
    minWidth: 180,
    align: 'center',
    fixed: 'right',
    customRender: ({ record }) => (
      <div>
        <a-button type="link" onclick={() => handleAddVersion(record)}>
          添加版本
        </a-button>
        <a-button type="link" onclick={() => handleEdit(record)}>
          编辑
        </a-button>
        <a-popconfirm
          title="确定删除吗？"
          ok-text="确定"
          cancel-text="取消"
          onConfirm={() => handleDel(record)}
        >
          <a-button type="link" danger>
            删除
          </a-button>
        </a-popconfirm>
      </div>
    ),
  },
];

const selectedRow = ref();

const onShowSizeChange = (current: number, pageSize: number) => {
  pagination.value.current = current;
  pagination.value.size = pageSize;
  refresh();
};

const rowSelection: TableProps['rowSelection'] = {
  columnWidth: 60,
  type: 'radio',
  onChange: (_, selectedRows) => {
    selectedRow.value = selectedRows[0];
  },
};

const { data, run, loading } = useRequest(queryByPage, {
  defaultParams: [
    {
      pageNum: pagination.value.current,
      pageSize: pagination.value.size,
      data: {},
    },
  ],
  onSuccess: res => {
    pagination.value.total = res.total;
  },
});

const refresh = () => {
  run({
    pageSize: pagination.value.size,
    pageNum: pagination.value.current,
    data: {},
  });
};

const handleAdd = () => {
  showDialog(h(<FormModal />))
    .then(refresh)
    .catch(() => {});
};

const handleAddVersion = (row: ModelRecord) => {
  showDialog(<FormModal detail={row} />)
    .then(refresh)
    .catch(() => {});
};

const handleEdit = (row: ModelRecord) => {
  showDialog(<FormModal detail={row} isUpdate />)
    .then(refresh)
    .catch(() => {});
};

const handleDel = async (row: any) => {
  const res = await deleteModel(row.sysDesModelId);
  if (res.code === 200) {
    message.success('删除成功');
    refresh();
  } else {
    message.error('删除失败');
  }
};

const handleGetTreeData = async () => {
  requestModelTree(selectedRow.value.sysDesModelId).then(() => {
    message.success('获取成功');
  });
};
</script>
