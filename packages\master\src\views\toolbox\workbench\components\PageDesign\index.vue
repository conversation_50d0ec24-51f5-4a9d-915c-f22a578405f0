<template>
  <div class="border border-[#f0f0f0] border-solid rounded h-full">
    <WorkBench
      :schema="toolParams.pageStructure"
      :show-header="false"
      :inject-source="injectSource"
      :inject-component-props="injectProps"
      @inject-component-props-change="handleChange"
    />
  </div>
</template>
<script setup lang="ts">
import type { ParamsMap } from '@/master/types/tool';
import { useToolContext } from '../../useTool';
import { WorkBench } from 'scene-designer';
import { ParamType } from '../../constants';
import { message } from 'ant-design-vue';

const { toolParams } = useToolContext();
const injectProps = {
  binding: {
    isInjectProps: true,
    label: '绑定参数',
    value: undefined,
    comp: 'a-select',
    compProps: {
      placeholder: '请选择',
      allowClear: true,
      dropdownMatchSelectWidth: false,
      options: [
        {
          label: '输入参数',
          options: toolParams.value.inputParams.map(item => ({
            ...item,
            label: item.name,
            value: item.label,
          })),
        },
        {
          label: '输出参数',
          options: toolParams.value.outputParams.map(item => ({
            ...item,
            label: item.name,
            value: item.label,
          })),
        },
      ],
    },
  },
};

const injectSource = toolParams.value.inputParams.concat(
  toolParams.value.outputParams
);

const handleChange = ({
  value,
  configSchema,
  renderSchema,
  option,
}: {
  value: string;
  configSchema: any;
  renderSchema: any;
  option?: Global.Option;
}) => {
  renderSchema.props.label = value;
  renderSchema.props.value = option?.defaultValue;
  renderSchema.props.suffix = option?.unit;
  configSchema.label.value = value;
  configSchema.suffix.value = option?.unit;
  configSchema.value.value = option?.defaultValue;
  const combindParams = [
    ...toolParams.value.inputParams,
    ...toolParams.value.outputParams,
  ];
  const param = combindParams.find(item => item.label === value)!;
  const paramsMap = {
    paramType: param.type === 'in' ? ParamType.input : ParamType.output,
    paramCode: renderSchema.id,
    command: param.name,
    dataFromType: '',
    dataFromCommand: '',
  };
  setParamsMap(paramsMap);
};

const setParamsMap = (data: ParamsMap) => {
  const { paramsMap } = toolParams.value;
  const index = paramsMap.findIndex(item => item.paramCode === data.paramCode);
  if (index > -1) {
    paramsMap[index] = data;
  } else {
    paramsMap.push(data);
  }
  toolParams.value.paramsMap = paramsMap;
};

const validate = async () => {
  const isValid = toolParams.value.pageStructure.length > 0;
  if (!isValid) {
    message.error('页面结构不能为空');
  }
  return isValid;
};

defineExpose({
  validate,
});
</script>
